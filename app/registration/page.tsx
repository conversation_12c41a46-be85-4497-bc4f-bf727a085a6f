import { Metadata } from "next"
import RegistrationClient from "@/components/registration/registration-client"

export const metadata: Metadata = {
  title: "Registration Information | IFMB 2025",
  description:
    "Registration information for the International Forum on Maize Biology 2025. Learn about registration fees, payment methods, and important deadlines.",
  keywords:
    "IFMB registration, maize biology conference registration, IFMB 2025 fees, conference payment, registration deadline",
}

/**
 * Registration information page for IFMB 2025
 */
export default function RegistrationPage() {
  // Conference information
  const conferenceInfo = {
    title: "International Forum on Maize Biology",
    shortTitle: "IFMB 2025",
    dates: "October 16-20, 2025",
    location: "Wuhan, Hubei Province, China",
    venue: "Huazhong Agricultural University",
    expectedAttendance: "500+",
  }

  // Registration fee standards
  const registrationFees = [
    {
      type: "Student",
      earlyBird: {
        price: 1400,
        currency: "CNY",
        deadline: "August 15, 2025",
      },
      regular: {
        price: 1600,
        currency: "CNY",
        deadline: "September 20, 2025",
      },
    },
    {
      type: "Non-student (postdocs, sponsors, PIs and teachers)",
      earlyBird: {
        price: 2200,
        currency: "CNY",
        deadline: "August 15, 2025",
      },
      regular: {
        price: 2400,
        currency: "CNY",
        deadline: "September 20, 2025",
      },
    },
  ]

  // Payment information
  const paymentInfo = {
    international: {
      beneficiary: "Huazhong Agricultural University",
      bank: "Bank of China Hubei Branch",
      accountNumber: "************",
      address: "Bank of China Hubei Branch, Donghu Subbranch 430079",
      swiftCode: "BKCHCNBJ600",
    },
    domestic: {
      beneficiary: "Huazhong Agricultural University",
      bank: "Bank of China Hubei Branch, Huanong Subbranch",
      beneficiaryNumber: "************",
      account: "************",
      address: "1 Shizishan St, Hongshan District, Wuhan 430070, China",
    },
  }

  // Important deadlines and information
  const importantInfo = {
    earlyRegistrationDeadline: "24:00, August 15, 2025",
    finalRegistrationDeadline: "24:00, September 20, 2025",
    registrationCloseTime: "24:00, September 20, 2025",
    notes: [
      'Please indicate "Name - Affiliation - IFMB2025" when making the payment. After successful payment, please upload the invoicing information and payment voucher to the website. If multiple attendees from the same affiliation register and pay together, please attach the names of all participants. For attendees from different affiliations, separate registration and payment are recommended.',
      "Registration is conducted online only and on-site registration is not accepted. The registration period starts from the date of the notice and ends on September 20, 2025.",
      "Early registration is considered complete before 24:00 on August 15, 2025 (based on the successful payment of the registration fee). The online registration channel will close at 24:00 on August 15, 2025.",
      "The registration fee covers meals and conference materials during the event. Please transfer the conference fee in advance. In principle, on-site registration is not accepted.",
      "Those who have registered and paid but are unable to attend due to unforeseen circumstances will not have their registration fees refunded. However, they can be represented by others at the conference.",
      "A valid registration is confirmed upon receipt of the registration fee by the conference organizing committee. The payment time is based on the time of remittance or transfer.",
      "Conference fee invoices will be issued and sent to the email address provided during registration during the conference.",
    ],
  }

  // What's included in registration
  const includedItems = [
    "Conference materials",
    "Coffee breaks",
    "Conference meals",
    "Abstract compendium",
    "Access to all sessions",
    "Networking events",
  ]

  // Contact information
  const contacts = [
    {
      name: "Registration Support",
      email: "<EMAIL>",
      // phone: "+86 27 8728 2045",
    },
  ]

  return (
    <RegistrationClient
      conferenceInfo={conferenceInfo}
      registrationFees={registrationFees}
      paymentInfo={paymentInfo}
      importantInfo={importantInfo}
      includedItems={includedItems}
      contacts={contacts}
    />
  )
}
