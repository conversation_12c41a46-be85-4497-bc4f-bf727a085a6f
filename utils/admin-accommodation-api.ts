/**
 * 管理员住宿相关的API调用工具
 * 提供管理员住宿管理功能的API接口
 */

import { api } from "@/utils/api"
import type {
  AdminReservationData,
  AdminReservationsApiResponse,
  HotelRoomData,
  ApiResponse,
} from "@/types/accommodation"

/**
 * 获取所有住宿预订记录
 * @returns Promise<AdminReservationData[]> 预订记录列表
 */
export async function getAdminReservations(): Promise<AdminReservationData[]> {
  try {
    const result = await api.get<
      | AdminReservationsApiResponse
      | AdminReservationData[]
    >("/api/admin/accommodation/reservations")

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200 && result.data) {
        return result.data
      } else {
        throw new Error(result.msg || "Failed to fetch reservations")
      }
    } else if (Array.isArray(result)) {
      // 处理直接返回数组的情况（向后兼容）
      return result
    } else {
      throw new Error("Invalid response format from server")
    }
  } catch (error) {
    console.error("Get admin reservations error:", error)
    throw error
  }
}

/**
 * 获取酒店列表 - 管理员版本
 * @returns Promise<any[]> 酒店列表
 */
export async function getAdminHotels(): Promise<any[]> {
  try {
    const result = await api.get<
      | {
          code?: number
          data?: any[]
          msg?: string
        }
      | any[]
    >("/api/admin/accommodation/hotels")

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200 && result.data) {
        return result.data
      } else {
        throw new Error(result.msg || "Failed to fetch hotels")
      }
    } else if (Array.isArray(result)) {
      // 处理直接返回数组的情况（向后兼容）
      return result
    } else {
      throw new Error("Invalid response format from server")
    }
  } catch (error) {
    console.error("Get admin hotels error:", error)
    throw error
  }
}

/**
 * 获取房间类型列表 - 管理员版本
 * @returns Promise<any[]> 房间类型列表
 */
export async function getAdminRooms(): Promise<any[]> {
  try {
    const result = await api.get<
      | {
          code?: number
          data?: any[]
          msg?: string
        }
      | any[]
    >("/api/admin/accommodation/rooms")

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200 && result.data) {
        return result.data
      } else {
        throw new Error(result.msg || "Failed to fetch rooms")
      }
    } else if (Array.isArray(result)) {
      // 处理直接返回数组的情况（向后兼容）
      return result
    } else {
      throw new Error("Invalid response format from server")
    }
  } catch (error) {
    console.error("Get admin rooms error:", error)
    throw error
  }
}

/**
 * 获取用户住宿信息 - 普通用户版本
 * @returns Promise<HotelRoomData[]> 住宿信息列表
 */
export async function getAccommodations(): Promise<HotelRoomData[]> {
  try {
    const result = await api.get<
      | {
          code?: number
          data?: HotelRoomData[]
          msg?: string
        }
      | HotelRoomData[]
    >("/api/accommodations")

    // 检查是否有标准API响应格式
    if (result && typeof result === "object" && "code" in result) {
      if (result.code === 200 && result.data) {
        return result.data
      } else {
        throw new Error(result.msg || "Failed to fetch accommodations")
      }
    } else if (Array.isArray(result)) {
      // 处理直接返回数组的情况（向后兼容）
      return result
    } else {
      throw new Error("Invalid response format from server")
    }
  } catch (error) {
    console.error("Get accommodations error:", error)
    throw error
  }
}
