/**
 * MinIO 工具函数
 * 提供统一的MinIO资源URL生成
 */

const MINIO_BASE_URL = "https://minioapi.bugmaker.me/ifmb-2025-public"

/**
 * 获取首页背景图片URL
 */
export function getHomePageImageUrl(): string {
  return `${MINIO_BASE_URL}/home-page.jpg`
}

/**
 * 获取favicon URL
 */
export function getFaviconUrl(): string {
  return `${MINIO_BASE_URL}/favicon.ico`
}

/**
 * 获取图案SVG URL
 */
export function getPatternSvgUrl(): string {
  return `${MINIO_BASE_URL}/pattern.svg`
}

/**
 * 获取报告者图片URL
 * @param region 地区
 * @param filename 文件名
 */
export function getReporterImageUrl(region: string, filename: string): string {
  return `${MINIO_BASE_URL}/reporters/${filename}`
}

/**
 * 获取文档URL
 * @param filename 文件名
 */
export function getDocumentUrl(filename: string): string {
  return `${MINIO_BASE_URL}/docs/${filename}`
}

/**
 * 获取酒店图片URL
 * @param imageUrl 图片路径（通常以 /ifmb-2025-public/ 开头）
 */
export function getHotelImageUrl(imageUrl: string): string {
  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith("http")) {
    return imageUrl
  }

  // 如果以 /ifmb-2025-public/ 开头，需要去掉这个前缀
  if (imageUrl.startsWith("/ifmb-2025-public/")) {
    return `${MINIO_BASE_URL}${imageUrl.substring("/ifmb-2025-public".length)}`
  }

  // 如果是相对路径，直接拼接
  return `${MINIO_BASE_URL}${imageUrl.startsWith("/") ? imageUrl : "/" + imageUrl}`
}

/**
 * 获取通用MinIO资源URL
 * @param path 资源路径
 */
export function getMinioUrl(path: string): string {
  // 如果已经是完整URL，直接返回
  if (path.startsWith("http")) {
    return path
  }

  // 如果以 /ifmb-2025-public/ 开头，使用基础URL
  if (path.startsWith("/ifmb-2025-public/")) {
    return `https://minioapi.bugmaker.me${path}`
  }

  // 否则使用完整的基础URL
  return `${MINIO_BASE_URL}${path.startsWith("/") ? path : "/" + path}`
}
