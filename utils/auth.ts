/**
 * 认证相关工具函数
 */

/**
 * 从 localStorage 获取认证 token
 * @returns 认证 token 或 null
 */
export function getAuthToken(): string | null {
  try {
    if (typeof window === "undefined") {
      return null
    }

    const userData = localStorage.getItem("user")
    if (!userData) {
      return null
    }

    const parsedData = JSON.parse(userData) as Record<string, unknown>
    return (parsedData.token as string) || ((parsedData.data as Record<string, unknown>)?.token as string) || null
  } catch (error) {
    return null
  }
}

/**
 * 清除用户认证信息并重定向到登录页
 * @param redirectUrl 重定向的登录页URL，默认为 '/login'
 * @param message 可选的错误消息
 */
export function clearAuthAndRedirect(redirectUrl: string = "/login", message?: string): void {
  try {
    if (typeof window !== "undefined") {
      // 清除localStorage中的用户数据
      localStorage.removeItem("user")

      // 如果有错误消息，存储到sessionStorage中，登录页可以显示
      if (message) {
        sessionStorage.setItem("auth_error_message", message)
      }

      // 重定向到登录页
      window.location.href = redirectUrl
    }
  } catch (error) {
    // Silently handle error in production
  }
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export function isAuthenticated(): boolean {
  return getAuthToken() !== null
}

/**
 * 获取当前用户信息
 * @returns 用户信息或 null
 */
export function getCurrentUser(): any | null {
  try {
    if (typeof window === "undefined") {
      return null
    }

    const userData = localStorage.getItem("user")
    if (!userData) {
      return null
    }

    const parsedData = JSON.parse(userData) as Record<string, unknown>
    return parsedData.user_info || (parsedData.data as Record<string, unknown>)?.user_info || parsedData
  } catch (error) {
    return null
  }
}

/**
 * 从后端获取最新的用户状态
 * @returns Promise<用户信息或 null>
 */
export async function getCurrentUserFromAPI(): Promise<any | null> {
  try {
    if (typeof window === "undefined") {
      return null
    }

    // 首先检查是否有token
    const token = getAuthToken()
    if (!token) {
      return null
    }

    // 从localStorage获取用户ID
    const currentUser = getCurrentUser()
    if (!currentUser || !currentUser.id) {
      return null
    }

    // 调用API获取最新用户信息
    const response = await authenticatedFetchJson(
      `${process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001"}/api/users/${currentUser.id}`,
      {
        method: "GET",
      }
    )

    if (response && response.data) {
      // 更新localStorage中的用户数据
      const userData = localStorage.getItem("user")
      if (userData) {
        const parsedData = JSON.parse(userData) as Record<string, unknown>

        // 保持原有的数据结构，只更新用户信息部分
        if (parsedData.user_info) {
          parsedData.user_info = { ...parsedData.user_info, ...response.data }
        } else if (parsedData.data && typeof parsedData.data === "object") {
          const dataObj = parsedData.data as Record<string, unknown>
          if (dataObj.user_info) {
            dataObj.user_info = { ...dataObj.user_info, ...response.data }
          }
        } else {
          // 如果结构不匹配，直接更新
          parsedData.user_info = response.data
        }

        localStorage.setItem("user", JSON.stringify(parsedData))
      }

      return response.data
    }

    return null
  } catch (error) {
    console.error("Failed to fetch current user from API:", error)
    // 如果API调用失败，返回localStorage中的数据作为fallback
    return getCurrentUser()
  }
}

/**
 * 刷新用户状态缓存
 * 用于在关键操作后强制刷新用户数据
 */
export async function refreshUserStatus(): Promise<void> {
  try {
    await getCurrentUserFromAPI()

    // 触发storage事件，通知其他组件用户数据已更新
    if (typeof window !== "undefined") {
      window.dispatchEvent(
        new StorageEvent("storage", {
          key: "user",
          newValue: localStorage.getItem("user"),
          oldValue: localStorage.getItem("user"),
        })
      )
    }
  } catch (error) {
    console.error("Failed to refresh user status:", error)
  }
}

/**
 * 创建带认证头的 fetch 请求配置
 * @param options 额外的 fetch 选项
 * @returns 包含认证头的 fetch 配置
 */
export function createAuthenticatedRequest(options: RequestInit = {}): RequestInit {
  const token = getAuthToken()

  return {
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
  }
}

/**
 * 检查认证状态，如果未认证则重定向到登录页
 * @param redirectUrl 重定向的登录页URL，默认为 '/login'
 * @returns 是否已认证
 */
export function requireAuth(redirectUrl: string = "/login"): boolean {
  if (!isAuthenticated()) {
    if (typeof window !== "undefined") {
      window.location.href = redirectUrl
    }
    return false
  }
  return true
}

/**
 * 统一的API请求处理函数，自动处理JWT过期和认证错误
 * @param url 请求URL
 * @param options fetch请求选项
 * @returns Promise<Response>
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const token = getAuthToken()

  // 检查是否需要跳过Content-Type设置（用于FormData请求）
  const skipContentType = options.headers && (options.headers as any)["X-Skip-Content-Type"]

  // 创建基础headers
  const baseHeaders: Record<string, string> = {}
  if (!skipContentType) {
    baseHeaders["Content-Type"] = "application/json"
  }
  if (token) {
    baseHeaders["Authorization"] = `Bearer ${token}`
  }

  // 创建最终headers，移除特殊标记
  const finalHeaders = { ...baseHeaders, ...options.headers }
  if (skipContentType) {
    delete (finalHeaders as any)["X-Skip-Content-Type"]
  }

  // 创建带认证头的请求配置
  const requestOptions: RequestInit = {
    ...options,
    headers: finalHeaders,
  }

  try {
    const response = await fetch(url, requestOptions)

    // 检查是否是认证相关错误
    if (response.status === 401) {
      // JWT过期或无效，清除认证信息并重定向
      clearAuthAndRedirect("/login", "Your session has expired. Please login again.")
      throw new Error("Authentication expired")
    }

    // 检查是否是权限不足错误
    if (response.status === 403) {
      throw new Error("Access denied. You don't have permission to perform this action.")
    }

    return response
  } catch (error) {
    // 如果是网络错误或其他错误，直接抛出
    if (error instanceof Error && error.message === "Authentication expired") {
      throw error
    }

    // 对于其他错误，检查是否可能是token相关问题
    if (error instanceof TypeError && error.message.includes("fetch")) {
      // 网络错误，可能是服务器不可达
      throw new Error("Network error. Please check your connection and try again.")
    }

    throw error
  }
}

/**
 * 统一的API JSON请求处理函数，自动处理JWT过期和JSON解析
 * @param url 请求URL
 * @param options fetch请求选项
 * @returns Promise<any> 解析后的JSON数据
 */
export async function authenticatedFetchJson(url: string, options: RequestInit = {}): Promise<any> {
  const response = await authenticatedFetch(url, options)

  try {
    const data = (await response.json()) as any

    // 检查业务层面的错误码
    if (!response.ok) {
      // 如果响应不成功，抛出包含服务器错误信息的错误
      const errorMessage = data.msg || data.message || `HTTP ${response.status}: ${response.statusText}`
      throw new Error(errorMessage)
    }

    // 检查自定义的错误码格式
    if (data.code && data.code !== 200) {
      const errorMessage = data.msg || data.message || "Request failed"
      throw new Error(errorMessage)
    }

    return data
  } catch (error) {
    // 如果JSON解析失败
    if (error instanceof SyntaxError) {
      throw new Error("Invalid response format from server")
    }

    // 重新抛出其他错误
    throw error
  }
}
