"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import Link from "next/link"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { fadeIn, staggerContainer } from "@/lib/animations"

// Types
type ConferenceInfo = {
  title: string
  shortTitle: string
  dates: string
  location: string
  venue: string
  expectedAttendance: string
}

type RegistrationFee = {
  type: string
  earlyBird: {
    price: number
    currency: string
    deadline: string
  }
  regular: {
    price: number
    currency: string
    deadline: string
  }
}

type PaymentInfo = {
  international: {
    beneficiary: string
    bank: string
    accountNumber: string
    address: string
    swiftCode: string
  }
  domestic: {
    beneficiary: string
    bank: string
    beneficiaryNumber: string
    account: string
    address: string
  }
}

type ImportantInfo = {
  earlyRegistrationDeadline: string
  finalRegistrationDeadline: string
  registrationCloseTime: string
  notes: string[]
}

type Contact = {
  name: string
  email: string
  phone?: string
}

type RegistrationClientProps = {
  conferenceInfo: ConferenceInfo
  registrationFees: RegistrationFee[]
  paymentInfo: PaymentInfo
  importantInfo: ImportantInfo
  includedItems: string[]
  contacts: Contact[]
}

export default function RegistrationClient({
  conferenceInfo,
  registrationFees,
  paymentInfo,
  importantInfo,
  includedItems,
  contacts,
}: RegistrationClientProps) {
  return (
    <main className="pb-20">
      {/* Hero Section */}
      <section className="relative overflow-hidden pt-32 pb-16 text-white">
        <div className="absolute inset-0">
          <Image
            src="https://minioapi.bugmaker.me/ifmb-2025-public/home-page.jpg"
            alt="Conference Background"
            fill
            priority
            className="object-cover object-top"
          />
          <div className="absolute inset-0 bg-black/30 backdrop-blur-sm"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-transparent to-green-900/30"></div>
          <div
            className="absolute inset-0 opacity-10"
            style={{ backgroundImage: `url('https://minioapi.bugmaker.me/ifmb-2025-public/pattern.svg')` }}
          ></div>
        </div>
        <div className="relative z-10 container mx-auto px-6">
          <div className="mx-auto max-w-4xl p-8 text-center">
            <Badge className="mb-4 border-green-400/50 bg-green-600/40 px-3 py-1 text-white">
              {conferenceInfo.shortTitle}
            </Badge>
            <h1 className="mb-6 text-4xl font-bold md:text-5xl">Registration Information</h1>
            <p className="mb-8 text-lg text-green-100">
              Learn about registration fees, payment methods, and important deadlines for IFMB 2025
            </p>
            <div className="grid grid-cols-1 gap-6 text-center md:grid-cols-3">
              <div className="rounded-lg bg-white/10 p-4 backdrop-blur-sm">
                <div className="text-2xl font-bold text-yellow-400">{conferenceInfo.dates}</div>
                <div className="text-sm text-green-100">Conference Dates</div>
              </div>
              <div className="rounded-lg bg-white/10 p-4 backdrop-blur-sm">
                <div className="text-2xl font-bold text-yellow-400">{conferenceInfo.expectedAttendance}</div>
                <div className="text-sm text-green-100">Expected Participants</div>
              </div>
              <div className="rounded-lg bg-white/10 p-4 backdrop-blur-sm">
                <div className="text-2xl font-bold text-yellow-400">Online Only</div>
                <div className="text-sm text-green-100">Registration Method</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Registration Fee Standards */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Registration Fee Standards</h2>
              <p className="text-lg text-gray-600">
                Inclusive of conference materials, coffee breaks, and conference meals
              </p>
            </motion.div>

            <motion.div variants={fadeIn("up", 0.2)} className="overflow-x-auto">
              <div className="min-w-full rounded-lg border border-gray-200 bg-white shadow-lg">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Registration Type</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                        Before August 15, 2025
                        <br />
                        <span className="text-xs font-normal text-gray-600">(inclusive)</span>
                      </th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                        Before September 20, 2025
                        <br />
                        <span className="text-xs font-normal text-gray-600">(inclusive)</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {registrationFees.map((fee, index) => (
                      <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                        <td className="px-6 py-4 text-sm font-medium text-gray-900">{fee.type}</td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-lg font-bold text-green-600">
                            {fee.earlyBird.price} {fee.earlyBird.currency}
                          </div>
                          <Badge className="mt-1 bg-green-100 text-green-800">Early Bird</Badge>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-lg font-bold text-gray-600">
                            {fee.regular.price} {fee.regular.currency}
                          </div>
                          <Badge variant="outline" className="mt-1">
                            Regular
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </motion.div>

            <motion.div variants={fadeIn("up", 0.3)} className="mt-8 text-center">
              <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-6">
                <div className="mb-2 flex items-center justify-center">
                  <i className="fas fa-clock mr-2 text-yellow-600"></i>
                  <span className="font-semibold text-yellow-800">Early Registration Deadline</span>
                </div>
                <p className="text-yellow-700">
                  {importantInfo.earlyRegistrationDeadline} (subject to the successful payment time of the registration
                  fee)
                </p>
                <p className="mt-2 text-sm text-yellow-600">
                  Travel and accommodation expenses are at the attendee's own cost.
                </p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* What's Included */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">What's Included</h2>
              <p className="text-lg text-gray-600">Your registration fee covers all essential conference services</p>
            </motion.div>

            <motion.div variants={fadeIn("up", 0.2)}>
              <Card className="p-6">
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    {includedItems.map((item, index) => (
                      <div key={index} className="flex items-center">
                        <i className="fas fa-check mr-3 text-green-600"></i>
                        <span className="text-gray-700">{item}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Payment Methods */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Payment Methods</h2>
              <p className="text-lg text-gray-600">Transfer funds to the account of Huazhong Agricultural University</p>
            </motion.div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="h-full p-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-globe mr-3 text-blue-600"></i>
                      International Attendees
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary:</span>
                      <div className="text-gray-900">{paymentInfo.international.beneficiary}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Bank:</span>
                      <div className="text-gray-900">{paymentInfo.international.bank}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Account No.:</span>
                      <div className="font-mono text-gray-900">{paymentInfo.international.accountNumber}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Address:</span>
                      <div className="text-gray-900">{paymentInfo.international.address}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">SWIFT Code:</span>
                      <div className="font-mono text-gray-900">{paymentInfo.international.swiftCode}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="h-full p-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-green-700">
                      <i className="fas fa-map-marker-alt mr-3 text-green-600"></i>
                      Domestic Attendees
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.beneficiary}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary's Bank:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.bank}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Beneficiary's No.:</span>
                      <div className="font-mono text-gray-900">{paymentInfo.domestic.beneficiaryNumber}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Account:</span>
                      <div className="font-mono text-gray-900">{paymentInfo.domestic.account}</div>
                    </div>
                    <div>
                      <span className="font-medium text-gray-600">Address:</span>
                      <div className="text-gray-900">{paymentInfo.domestic.address}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.4)}>
                <Card className="h-full p-6">
                  <CardHeader>
                    <CardTitle className="flex items-center text-orange-700">
                      <i className="fas fa-mobile-alt mr-3 text-orange-600"></i>
                      Alipay Payment
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-center">
                      <p className="mb-4 text-sm text-gray-600">Scan QR code to pay with Alipay</p>
                      <div className="mx-auto mb-4 max-w-48">
                        <img
                          src="https://minioapi.bugmaker.me/ifmb-2025-public/支付宝.jpg"
                          alt="Alipay QR Code"
                          className="w-full rounded-lg border border-gray-200 shadow-sm"
                        />
                      </div>
                      <div className="text-xs text-gray-500">
                        <p>Please include your name and "IFMB2025" in the payment note</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Important Notes */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-6xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Important Notes</h2>
              <p className="text-lg text-gray-600">Please read these important registration guidelines carefully</p>
            </motion.div>

            {/* Key Deadlines */}
            <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-2">
              <motion.div variants={fadeIn("up", 0.2)}>
                <Card className="h-full border-red-200 bg-red-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-red-700">
                      <i className="fas fa-exclamation-triangle mr-3"></i>
                      Registration Deadlines
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <div className="text-sm text-gray-600">Early Registration:</div>
                      <div className="text-lg font-bold text-red-600">{importantInfo.earlyRegistrationDeadline}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Final Registration:</div>
                      <div className="text-lg font-bold text-red-600">{importantInfo.finalRegistrationDeadline}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Online Channel Closes:</div>
                      <div className="text-lg font-bold text-red-600">{importantInfo.registrationCloseTime}</div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              <motion.div variants={fadeIn("up", 0.3)}>
                <Card className="h-full border-blue-200 bg-blue-50 p-6">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center text-blue-700">
                      <i className="fas fa-info-circle mr-3"></i>
                      Registration Method
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <i className="fas fa-laptop mr-3 text-blue-600"></i>
                        <span className="text-gray-700">Online registration only</span>
                      </div>
                      <div className="flex items-center">
                        <i className="fas fa-times mr-3 text-red-500"></i>
                        <span className="text-gray-700">No on-site registration accepted</span>
                      </div>
                      <div className="flex items-center">
                        <i className="fas fa-credit-card mr-3 text-blue-600"></i>
                        <span className="text-gray-700">Payment required for confirmation</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Detailed Notes */}
            <motion.div variants={fadeIn("up", 0.4)}>
              <Card className="p-6">
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <i className="fas fa-clipboard-list mr-3 text-green-600"></i>
                    Registration Guidelines
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {importantInfo.notes.map((note, index) => (
                      <div key={index} className="flex items-start">
                        <div className="mt-0.5 mr-3 flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-full bg-green-100">
                          <span className="text-xs font-semibold text-green-600">{index + 1}</span>
                        </div>
                        <p className="text-sm leading-relaxed text-gray-700">{note}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-green-50 py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl text-center"
          >
            <motion.div variants={fadeIn("up", 0.1)}>
              <h2 className="mb-6 text-3xl font-bold text-gray-900">Ready to Register?</h2>
              <p className="mb-8 text-lg text-gray-600">
                Join the premier international conference on maize biology and connect with leading researchers
                worldwide.
              </p>
              <div className="flex flex-col justify-center gap-4 sm:flex-row">
                <Link href="/register">
                  <Button className="bg-green-600 px-8 py-3 text-lg text-white hover:bg-green-700">
                    <i className="fas fa-user-plus mr-2"></i>
                    Register Now
                  </Button>
                </Link>
                <Link href="/login">
                  <Button
                    variant="outline"
                    className="border-green-600 px-8 py-3 text-lg text-green-600 hover:bg-green-50"
                  >
                    <i className="fas fa-sign-in-alt mr-2"></i>
                    Already Registered? Login
                  </Button>
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-6">
          <motion.div
            initial="hidden"
            whileInView="show"
            viewport={{ once: true }}
            variants={staggerContainer(0.1)}
            className="mx-auto max-w-4xl"
          >
            <motion.div variants={fadeIn("up", 0.1)} className="mb-12 text-center">
              <h2 className="mb-4 text-3xl font-bold text-gray-900">Contact Information</h2>
              <p className="text-lg text-gray-600">Need help with registration? Contact our support team</p>
            </motion.div>

            <div className="grid grid-cols-1 gap-8 md:grid-cols-1">
              {contacts.map((contact, index) => (
                <motion.div key={contact.name} variants={fadeIn("up", 0.1 * (index + 2))}>
                  <Card className="p-6 text-center">
                    <div className="mb-4">
                      <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                        <i className="fas fa-headset text-2xl text-green-600"></i>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">{contact.name}</h3>
                    </div>
                    <div className="space-y-3">
                      {contact.phone && (
                        <div className="flex items-center justify-center">
                          <i className="fas fa-phone mr-3 text-green-600"></i>
                          <a href={`tel:${contact.phone}`} className="text-gray-700 hover:text-green-600">
                            {contact.phone}
                          </a>
                        </div>
                      )}
                      <div className="flex items-center justify-center">
                        <i className="fas fa-envelope mr-3 text-green-600"></i>
                        <a href={`mailto:${contact.email}`} className="text-gray-700 hover:text-green-600">
                          {contact.email}
                        </a>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  )
}
