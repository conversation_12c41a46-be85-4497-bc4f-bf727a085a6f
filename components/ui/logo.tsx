/**
 * Logo 组件
 * 专门用于显示 IFMB Logo，避免 favicon.ico 的优化问题
 */

"use client"

import { getFaviconUrl } from "@/utils/minio"

interface LogoProps {
  size?: number
  className?: string
  alt?: string
}

export default function Logo({ size = 24, className = "", alt = "IFMB Logo" }: LogoProps) {
  return (
    // eslint-disable-next-line @next/next/no-img-element
    <img
      src={getFaviconUrl()}
      alt={alt}
      width={size}
      height={size}
      className={`object-contain ${className}`}
      loading="eager"
      // 避免 Next.js 图片优化
      style={{ maxWidth: "none" }}
    />
  )
}

/**
 * 圆形 Logo 组件
 */
export function CircularLogo({ size = 40, className = "" }: Omit<LogoProps, "alt">) {
  return (
    <div
      className={`flex items-center justify-center overflow-hidden rounded-full border-2 border-yellow-500 bg-white shadow-md transition-all duration-300 hover:scale-105 ${className}`}
      style={{ width: size, height: size }}
    >
      <Logo size={Math.floor(size * 0.6)} alt="IFMB Logo" />
    </div>
  )
}
