"use client"

import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { countries } from "@/data/countries"
import { getCurrentUser } from "@/utils/auth"
import { api } from "@/utils/api"

const membershipTypes = [
  { id: 1, value: "1", label: "Student" },
  { id: 2, value: "2", label: "Non-Student" },
]

const roleTypes = [
  { id: 1, value: "1", label: "Admin" },
  { id: 2, value: "2", label: "User" },
]

const genders = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
]

interface User {
  uid: number
  membership_id: number
  email_id: number
  username: string
  avatar_url: string
  name: string
  gender: string
  country: string
  organization: string
  phone: string
  position: string
  bio: string
  interest: string
  expertise: string
  paid: boolean
  u_create_time: string
  role: string
  mid: number
  membership: string
  Price: number
  eid: number
  email: string
  email_verified: boolean
  VerifiedTime: string
}

interface EditUserModalProps {
  user: User | null
  open: boolean
  onClose: () => void
  onUserUpdated: (user: User) => void
}

export default function EditUserModal({ user, open, onClose, onUserUpdated }: EditUserModalProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [showCountryDropdown, setShowCountryDropdown] = useState(false)
  const [currentUser, setCurrentUser] = useState<Record<string, unknown> | null>(null)
  const [form, setForm] = useState({
    username: "",
    name: "",
    email: "",
    gender: "male",
    role: "1",
    membership: "1",
    country: "",
  })

  const [formErrors, setFormErrors] = useState({
    username: "",
    email: "",
    country: "",
  })

  // Helper functions to map API values to form IDs
  const getRoleId = (roleName: string) => {
    return roleName === "admin" ? "1" : "2"
  }

  const getMembershipId = (membershipName: string) => {
    if (membershipName === "Student") return "1"
    if (membershipName === "Non-Student" || membershipName === "Non-Student Member") return "2"
    return "1" // default to Student
  }

  // Check if current user is editing themselves
  const isEditingSelf = () => {
    if (!currentUser || !user) return false
    // Compare by username or id
    return currentUser.username === user.username || currentUser.id?.toString() === user.uid?.toString()
  }

  // Get current logged-in user information
  useEffect(() => {
    const current = getCurrentUser()
    setCurrentUser(current)
  }, [])

  // Update form when user changes
  useEffect(() => {
    if (user) {
      setForm({
        username: user.username || "",
        name: user.name || "",
        email: user.email || "",
        gender: user.gender || "male",
        role: getRoleId(user.role || "user"),
        // 优先使用 membership_id 转为字符串，如果不存在则使用 membership 映射
        membership: user.membership_id ? String(user.membership_id) : getMembershipId(user.membership || "Student"),
        country: user.country || "",
      })
    }
  }, [user])

  useEffect(() => {
    // 添加点击事件监听器，用于关闭国家下拉框
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest("#edit-user-country-dropdown-container")) {
        setShowCountryDropdown(false)
      }
    }

    if (showCountryDropdown) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    // 清理函数
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showCountryDropdown])

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    return emailRegex.test(email)
  }

  const validateField = (name: string, value: string) => {
    let error = ""

    switch (name) {
      case "username":
        if (value.length < 3) {
          error = "Username must be at least 3 characters"
        }
        break
      case "email":
        if (value.length > 64) {
          error = "Email address must be at most 64 characters"
        }
        if (!validateEmail(value)) {
          error = "Please enter a valid email address"
        }
        break
      case "country":
        if (!countries.includes(value)) {
          error = "Please select a country from the list"
        }
        break
    }

    return error
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })

    // Validate field if it's one we care about
    if (["username", "email", "country"].includes(name)) {
      const error = validateField(name, value)
      setFormErrors((prev) => ({ ...prev, [name]: error }))
    }
  }

  const handleCountryChange = (country: string) => {
    setForm({ ...form, country })
    setShowCountryDropdown(false)

    // Validate country
    const error = validateField("country", country)
    setFormErrors((prev) => ({ ...prev, country: error }))
  }

  const validateForm = () => {
    const newErrors = {
      username: validateField("username", form.username),
      email: validateField("email", form.email),
      country: validateField("country", form.country),
    }

    setFormErrors(newErrors)

    // Check if there are any errors
    return !Object.values(newErrors).some((errorMsg) => errorMsg !== "")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) return

    // Form validation
    if (!validateForm()) {
      setError("Please fix the errors in the form")
      return
    }

    setLoading(true)
    setError("")

    try {
      // 获取认证token
      let authToken = null
      try {
        const userData = localStorage.getItem("user")
        if (userData) {
          const parsedData = JSON.parse(userData) as Record<string, unknown>
          authToken = (parsedData.token as string) || ((parsedData.data as Record<string, unknown>)?.token as string)
        }
      } catch (_error) {
        console.error("Error getting auth token:", _error)
      }

      if (!authToken) {
        setError("Authentication token not found. Please login again.")
        return
      }

      // 使用API工具类发送请求
      const result = await api.put<{
        code?: number
        data?: unknown
        msg?: string
        message?: string
      }>(`/api/admin/users/${user.uid}`, {
        username: form.username,
        name: form.name,
        email: form.email,
        gender: form.gender,
        role: parseInt(form.role),
        membership: parseInt(form.membership),
        country: form.country,
      })

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 0 || result.code === 200) {
          // Success - map form IDs back to API values
          const updatedUser = {
            ...user,
            username: form.username,
            name: form.name,
            email: form.email,
            gender: form.gender,
            role: form.role === "1" ? "admin" : "user",
            membership: form.membership === "1" ? "Student" : "Non-Student Member",
            country: form.country,
          }
          onUserUpdated(updatedUser)
          onClose()
        } else {
          setError(result.msg || result.message || "Failed to update user")
        }
      } else {
        // 处理直接返回成功的情况（向后兼容）
        const updatedUser = {
          ...user,
          username: form.username,
          name: form.name,
          email: form.email,
          gender: form.gender,
          role: form.role === "1" ? "admin" : "user",
          membership: form.membership === "1" ? "Student" : "Non-Student Member",
          country: form.country,
        }
        onUserUpdated(updatedUser)
        onClose()
      }
    } catch (_error) {
      console.error("Update user error:", _error)
      setError("An error occurred while updating the user")
    } finally {
      setLoading(false)
    }
  }

  if (!user) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-3xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Basic Information</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label htmlFor="username" className="mb-1 block text-sm font-medium text-gray-700">
                  Username <span className="text-red-500">*</span>
                </label>
                <Input
                  id="username"
                  name="username"
                  placeholder="Enter username (min. 3 characters)"
                  value={form.username}
                  onChange={handleChange}
                  required
                  disabled
                  className={`w-full bg-gray-100 ${formErrors.username ? "border-red-500" : ""}`}
                />
                {formErrors.username && <p className="mt-1 text-xs text-red-500">{formErrors.username}</p>}
                <p className="mt-1 text-xs text-gray-500">Username cannot be modified</p>
              </div>

              <div>
                <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                  Full Name <span className="text-red-500">*</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Enter full name"
                  value={form.name}
                  onChange={handleChange}
                  required
                  className="w-full"
                />
              </div>

              <div>
                <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                  Email Address <span className="text-red-500">*</span>
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={form.email}
                  onChange={handleChange}
                  required
                  className={`w-full ${formErrors.email ? "border-red-500" : ""}`}
                />
                {formErrors.email && <p className="mt-1 text-xs text-red-500">{formErrors.email}</p>}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Additional Information</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div>
                <label htmlFor="gender" className="mb-1 block text-sm font-medium text-gray-700">
                  Gender <span className="text-red-500">*</span>
                </label>
                <select
                  id="gender"
                  name="gender"
                  value={form.gender}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                  required
                >
                  {genders.map((item) => (
                    <option key={item.value} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="role" className="mb-1 block text-sm font-medium text-gray-700">
                  Role <span className="text-red-500">*</span>
                  {isEditingSelf() && (
                    <span className="ml-2 text-xs text-amber-600">(Cannot modify your own role)</span>
                  )}
                </label>
                <select
                  id="role"
                  name="role"
                  value={form.role}
                  onChange={handleChange}
                  disabled={isEditingSelf()}
                  className={`w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none ${
                    isEditingSelf() ? "cursor-not-allowed bg-gray-100 opacity-60" : ""
                  }`}
                  required
                >
                  {roleTypes.map((item) => (
                    <option key={item.id} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
                {isEditingSelf() && (
                  <p className="mt-1 text-xs text-amber-600">
                    For security reasons, administrators cannot modify their own role permissions.
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="membership" className="mb-1 block text-sm font-medium text-gray-700">
                  Membership Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="membership"
                  name="membership"
                  value={form.membership}
                  onChange={handleChange}
                  className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                  required
                >
                  {membershipTypes.map((item) => (
                    <option key={item.id} value={item.value}>
                      {item.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label htmlFor="country" className="mb-1 block text-sm font-medium text-gray-700">
                Country <span className="text-red-500">*</span>
              </label>
              <div className="relative" id="edit-user-country-dropdown-container">
                <Input
                  id="country"
                  name="country"
                  value={form.country}
                  onChange={(e) => {
                    handleChange(e)
                    setShowCountryDropdown(true)
                  }}
                  className={`w-full ${formErrors.country ? "border-red-500" : ""}`}
                  placeholder="Type to search countries"
                  onFocus={() => setShowCountryDropdown(true)}
                  required
                />
                {formErrors.country && <p className="mt-1 text-xs text-red-500">{formErrors.country}</p>}
                {showCountryDropdown && (
                  <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-white shadow-lg">
                    {countries
                      .filter((country) => country.toLowerCase().includes((form.country || "").toLowerCase()))
                      .slice(0, 5)
                      .map((country, index) => (
                        <div
                          key={index}
                          className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                          onClick={() => handleCountryChange(country)}
                        >
                          {country}
                        </div>
                      ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Error message */}
          {error && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
              <span className="text-sm text-red-600">{error}</span>
            </div>
          )}

          {/* Submit button */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Updating User..." : "Update User"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
