"use client"

import { motion } from "framer-motion"
import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import Pagination from "@/components/ui/pagination"
import { useToast } from "@/components/ui/toast"

import { api } from "@/utils/api"
import AddUserModal from "./add-user-modal"
import EditUserModal from "./edit-user-modal"

// API响应类型定义
type ApiResponse<T> = {
  code: number
  data: T
  msg: string
}

// Users API响应数据结构
type UsersApiData = {
  total: number
  users: User[]
}

// 用户数据类型定义 - 匹配API响应结构
interface User {
  uid: number
  membership_id: number
  email_id: number
  username: string
  avatar_url: string
  name: string
  gender: string
  country: string
  organization: string
  phone: string
  position: string
  bio: string
  interest: string
  expertise: string
  paid: boolean
  u_create_time: string
  role: string
  mid: number
  membership: string
  Price: number
  eid: number
  email: string
  email_verified: boolean
  VerifiedTime: string
}

export default function AdminUsersPage() {
  const { addToast } = useToast()

  // 状态管理
  const [allUsers, setAllUsers] = useState<User[]>([]) // 所有缓存的数据
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0) // 总记录数
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showEditModal, setShowEditModal] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // 分页配置
  const itemsPerPage = 20
  const fetchLimit = 100

  // 安全的日期格式化函数
  const formatDate = (dateString: string, options: { includeTime?: boolean } = {}) => {
    if (!isClient || !dateString) return ""
    try {
      const date = new Date(dateString)
      if (isNaN(date.getTime())) return "Invalid Date"
      return options.includeTime ? date.toLocaleString() : date.toLocaleDateString()
    } catch (_error) {
      return "Invalid Date"
    }
  }

  // 获取用户数据的API函数
  const fetchUsers = useCallback(
    async (page: number = 1, filter: string = "") => {
      setLoading(true)
      try {
        const params = {
          limit: itemsPerPage.toString(),
          offset: ((page - 1) * itemsPerPage).toString(),
          filter: filter,
        }

        const result = await api.get<
          | {
              code?: number
              data?: UsersApiData
              msg?: string
            }
          | UsersApiData
        >("/api/admin/users", params)

        let usersData: UsersApiData | null = null

        // 检查是否有标准API响应格式
        if (result && typeof result === "object" && "code" in result) {
          if (result.code === 200 && result.data) {
            usersData = result.data
          }
        } else if (result && typeof result === "object" && "users" in result) {
          // 处理直接返回数据的情况（向后兼容）
          usersData = result as UsersApiData
        }

        if (usersData) {
          const newUsers = (usersData.users || []).filter((user) => user && user.uid)
          setAllUsers(newUsers)
          setTotalCount(usersData.total || 0)
        } else {
          setAllUsers([])
          setTotalCount(0)
        }
      } catch (error) {
        console.error("Error fetching users:", error)
        addToast({
          type: "error",
          title: "Error",
          message: error instanceof Error ? error.message : "Failed to fetch user data",
        })
      } finally {
        setLoading(false)
      }
    },
    [addToast, itemsPerPage]
  )

  // 初始化数据加载
  useEffect(() => {
    setIsClient(true)
    fetchUsers(1, "")
  }, [fetchUsers])

  // 过滤和分页逻辑（搜索在服务器端处理）
  const filteredUsers = allUsers

  // 分页逻辑
  const totalPages = Math.ceil(totalCount / itemsPerPage)
  const paginatedUsers = filteredUsers

  // 处理搜索输入变化（不立即搜索）
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
  }

  // 处理回车搜索
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      setCurrentPage(1)
      fetchUsers(1, searchTerm)
    }
  }

  // 处理分页变化
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    fetchUsers(page, searchTerm)
  }

  const handleUserAdded = (_newUser: unknown) => {
    // 刷新用户列表而不是直接添加，因为类型不匹配
    fetchUsers(currentPage, searchTerm)
  }

  const handleUserUpdated = (_updatedUser: User) => {
    // 刷新整个用户列表以获取最新数据
    fetchUsers(currentPage, searchTerm)
  }

  const handleEditUser = (user: User) => {
    setEditingUser(user)
    setShowEditModal(true)
  }

  const getMembershipLabel = (membershipName: string) => {
    return membershipName || "Unknown"
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">Manage registered users and their permissions</p>
        </div>
        <AddUserModal onUserAdded={handleUserAdded} />
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Search users by name, email, or username... (Press Enter to search)"
                value={searchTerm}
                onChange={handleSearchInputChange}
                onKeyPress={handleSearchKeyPress}
                className="w-full"
              />
            </div>
            <Button variant="outline">
              <i className="fas fa-filter mr-2"></i>
              Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* User List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>All Users ({totalCount})</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Username</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Name</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Email</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Role</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Membership</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Country</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Organization</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Email Verified</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Paid</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Registered</th>
                  <th className="px-4 py-3 text-left font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {paginatedUsers.map((user) => {
                  if (!user || !user.uid) return null
                  return (
                    <motion.tr
                      key={user.uid}
                      className="border-b border-gray-100 hover:bg-gray-50"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <td className="px-4 py-3 font-medium">{user.username || "-"}</td>
                      <td className="px-4 py-3">{user.name || "-"}</td>
                      <td className="px-4 py-3">{user.email || "-"}</td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user.role === "admin"
                              ? "border-purple-200 bg-purple-50 text-purple-700"
                              : "border-blue-200 bg-blue-50 text-blue-700"
                          }
                        >
                          {user.role || "user"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Badge variant="outline" className="border-gray-200 bg-gray-50 text-gray-700">
                          {getMembershipLabel(user.membership)}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">{user.country || "-"}</td>
                      <td className="px-4 py-3">
                        <div className="max-w-32 truncate text-sm text-gray-600" title={user.organization || ""}>
                          {user.organization || "-"}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user["email_verified"]
                              ? "border-green-200 bg-green-50 text-green-700"
                              : "border-red-200 bg-red-50 text-red-700"
                          }
                        >
                          {user["email_verified"] ? "Verified" : "Unverified"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3">
                        <Badge
                          variant="outline"
                          className={
                            user.paid
                              ? "border-green-200 bg-green-50 text-green-700"
                              : "border-yellow-200 bg-yellow-50 text-yellow-700"
                          }
                        >
                          {user.paid ? "Paid" : "Unpaid"}
                        </Badge>
                      </td>
                      <td className="px-4 py-3 text-sm">{formatDate(user.u_create_time)}</td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                            onClick={() => handleEditUser(user)}
                            title="Edit user"
                          >
                            <i className="fas fa-edit text-gray-500"></i>
                          </Button>
                        </div>
                      </td>
                    </motion.tr>
                  )
                })}
              </tbody>
            </table>
          </div>

          {/* 分页组件 */}
          {totalPages > 1 && (
            <div className="mt-6 border-t pt-4">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
                itemsPerPage={itemsPerPage}
                totalItems={totalCount}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Modal */}
      <EditUserModal
        user={editingUser}
        open={showEditModal}
        onClose={() => {
          setShowEditModal(false)
          setEditingUser(null)
        }}
        onUserUpdated={handleUserUpdated}
      />
    </div>
  )
}
