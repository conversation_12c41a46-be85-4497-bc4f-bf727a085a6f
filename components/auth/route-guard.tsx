"use client"

import { usePathname, useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import AccessDeniedPage from "@/components/auth/access-denied-page"
import PageLoading from "@/components/layout/page-loading"
import { hasPermission, isPathInList, permissionConfig } from "@/config/permissions"

interface RouteGuardProps {
  children: React.ReactNode
  customPermissions?: boolean // 是否使用自定义权限配置而不是默认配置
  showAccessDenied?: boolean // 是否显示访问拒绝页面而不是重定向
}

type AccessDeniedReason = "not-logged-in" | "email-not-verified" | "not-admin" | "not-paid" | "insufficient-permissions"

export default function RouteGuard({
  children,
  customPermissions: _customPermissions = false,
  showAccessDenied = true,
}: RouteGuardProps) {
  const router = useRouter()
  const pathname = usePathname()
  const [authorized, setAuthorized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [accessDeniedInfo, setAccessDeniedInfo] = useState<{
    reason: AccessDeniedReason
    userRole?: string
    userEmail?: string
    attemptedPath?: string
  } | null>(null)

  useEffect(() => {
    // 检查用户权限
    const checkPermissions = () => {
      // 从localStorage获取用户信息
      const userJson = typeof window !== "undefined" ? localStorage.getItem("user") : null

      // 默认为未登录状态
      let isLoggedIn = false
      let isAdmin = false
      let isEmailVerified = false
      let isPaid = false
      let userRole = "guest"
      let userEmail = ""

      // 如果有用户信息，解析并获取权限相关字段
      if (userJson) {
        try {
          const parsedData = JSON.parse(userJson) as Record<string, unknown>
          // 提取用户信息，支持不同的数据结构
          const userInfo = parsedData.user_info || (parsedData.data as Record<string, unknown>)?.user_info || parsedData
          const userInfoTyped = userInfo as Record<string, unknown>

          isLoggedIn = true
          isAdmin = (userInfoTyped.role as string) === "admin"
          userRole = (userInfoTyped.role as string) || "user"
          userEmail = (userInfoTyped.email as string) || ""

          // 只使用后端返回的email_verified字段
          isEmailVerified = (userInfoTyped.email_verified as boolean) === true
          // 强制设置付费状态为 true - 移除付费验证
          isPaid = true
        } catch (_error) {
          // 即使解析失败，也保持所有状态为 false
        }
      }

      // 检查当前路径是否有权限访问
      const currentPath = pathname || ""
      const hasAccess = hasPermission(currentPath, isLoggedIn, isAdmin, isEmailVerified, isPaid)

      if (!hasAccess) {
        // 无权访问当前路径
        setAuthorized(false)

        if (showAccessDenied) {
          // 显示访问拒绝页面
          let reason: AccessDeniedReason = "insufficient-permissions"

          if (!isLoggedIn) {
            reason = "not-logged-in"
          } else if (!isEmailVerified && !isAdmin) {
            reason = "email-not-verified"
            // 存储用户尝试访问的页面，以便验证后重定向
            if (typeof window !== "undefined") {
              const currentPath = pathname || ""
              if (!currentPath.startsWith("/dashboard") && currentPath !== "/profile") {
                sessionStorage.setItem("redirectAfterVerification", currentPath)
              }
            }
          } else if (isLoggedIn && !isAdmin && currentPath.startsWith("/admin")) {
            reason = "not-admin"
          }
          // 移除付费检查逻辑 - 不再检查付费状态
          // } else if (isLoggedIn && isEmailVerified && !isPaid && !isAdmin) {
          //   // 检查是否访问需要付费的页面
          //   if (isPathInList(currentPath, permissionConfig.paidUserPaths)) {
          //     reason = "not-paid"
          //   }
          // }

          setAccessDeniedInfo({
            reason,
            userRole,
            userEmail,
            attemptedPath: currentPath,
          })
        } else {
          // 传统的重定向行为
          if (!isLoggedIn) {
            router.push("/login")
          } else if (!isEmailVerified && !isAdmin) {
            if (typeof window !== "undefined") {
              const currentPath = pathname || ""
              if (!currentPath.startsWith("/dashboard") && currentPath !== "/profile") {
                sessionStorage.setItem("redirectAfterVerification", currentPath)
              }
            }
            router.push("/dashboard")
          } else {
            router.push("/dashboard")
          }
        }
      } else {
        // 有权访问当前路径
        setAuthorized(true)
        setAccessDeniedInfo(null)
      }

      setIsLoading(false)
    }

    // 首次加载时检查
    checkPermissions()

    // 监听路由变化
    const handleRouteChange = () => {
      checkPermissions()
    }

    // 添加路由变化监听
    window.addEventListener("popstate", handleRouteChange)

    return () => {
      // 清理监听器
      window.removeEventListener("popstate", handleRouteChange)
    }
  }, [pathname, router])

  // 显示加载状态、访问拒绝页面或已授权的内容
  if (isLoading) {
    return <PageLoading />
  }

  if (!authorized && accessDeniedInfo) {
    return (
      <AccessDeniedPage
        reason={accessDeniedInfo.reason}
        userRole={accessDeniedInfo.userRole}
        userEmail={accessDeniedInfo.userEmail}
        attemptedPath={accessDeniedInfo.attemptedPath}
      />
    )
  }

  return authorized ? <>{children}</> : null
}
