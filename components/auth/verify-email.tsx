"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { api } from "@/utils/api"

export default function VerifyEmail() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const [verificationStatus, setVerificationStatus] = useState<"loading" | "success" | "error" | "invalid">("loading")
  const [message, setMessage] = useState("")
  const token = searchParams.get("token")

  useEffect(() => {
    const verifyEmail = async () => {
      if (!token) {
        setVerificationStatus("invalid")
        setMessage("Invalid verification link. No token provided.")
        return
      }
      try {
        // 使用API工具类调用后端API验证邮箱
        const result = await api.post<{
          code?: number
          data?: unknown
          msg?: string
          message?: string
        }>("/api/verify/email", { token: token })

        // 检查是否有标准API响应格式
        if (result && typeof result === "object" && "code" in result) {
          if (result.code === 200) {
            setVerificationStatus("success")
            setMessage("Your email has been successfully verified!")
          } else {
            setVerificationStatus("error")
            setMessage(result.msg || result.message || "Failed to verify email. Please try again or contact support.")
            return
          }
        } else {
          // 处理直接返回成功的情况（向后兼容）
          setVerificationStatus("success")
          setMessage("Your email has been successfully verified!")
        }

        // Update user data in localStorage to mark email as verified
        const userData = localStorage.getItem("user")
        if (userData) {
          try {
            // 添加更健壮的JSON解析错误处理
            let user
            try {
              user = JSON.parse(userData) as unknown
            } catch (_jsonError) {
              return
            }

            // 更新用户数据中的邮箱验证字段
            const userTyped = user as Record<string, unknown>
            if (userTyped.user_info) {
              const userInfo = userTyped.user_info as Record<string, unknown>
              userInfo.email_verified = true
            } else if ((userTyped.data as Record<string, unknown>)?.user_info) {
              const userInfo = (userTyped.data as Record<string, unknown>).user_info as Record<string, unknown>
              userInfo.email_verified = true
            }

            localStorage.setItem("user", JSON.stringify(user))
          } catch (_error) {
            console.error("Error updating user data:", _error)
          }
        }
      } catch (_error) {
        console.error("Email verification error:", _error)
        setVerificationStatus("error")
        setMessage("Failed to verify email. Please try again or contact support.")
      }
    }

    verifyEmail()
  }, [token])

  const handleContinue = () => {
    // Redirect to dashboard after successful verification
    router.push("/dashboard")
  }

  const getStatusIcon = () => {
    switch (verificationStatus) {
      case "loading":
        return <div className="mx-auto h-16 w-16 animate-spin rounded-full border-b-2 border-green-600"></div>
      case "success":
        return <i className="fas fa-check-circle mx-auto block text-6xl text-green-600"></i>
      case "error":
        return <i className="fas fa-times-circle mx-auto block text-6xl text-red-600"></i>
      case "invalid":
        return <i className="fas fa-exclamation-triangle mx-auto block text-6xl text-amber-600"></i>
      default:
        return null
    }
  }

  const getStatusColor = () => {
    switch (verificationStatus) {
      case "success":
        return "text-green-800"
      case "error":
        return "text-red-800"
      case "invalid":
        return "text-amber-800"
      default:
        return "text-gray-800"
    }
  }

  const getBackgroundColor = () => {
    switch (verificationStatus) {
      case "success":
        return "bg-green-50 border-green-200"
      case "error":
        return "bg-red-50 border-red-200"
      case "invalid":
        return "bg-amber-50 border-amber-200"
      default:
        return "bg-gray-50 border-gray-200"
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-green-50 via-blue-50 to-amber-50 px-4 py-12 pt-24 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-2xl"
      >
        <Card className={`${getBackgroundColor()} shadow-xl`}>
          <CardHeader className="pt-8 pb-6 text-center">
            <div className="mb-6">{getStatusIcon()}</div>
            <CardTitle className={`text-3xl font-bold ${getStatusColor()} mb-4`}>
              {verificationStatus === "loading" && "Verifying Email..."}
              {verificationStatus === "success" && "Email Verified!"}
              {verificationStatus === "error" && "Verification Failed"}
              {verificationStatus === "invalid" && "Invalid Link"}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-8 px-8 pb-8 text-center">
            <p className={`text-xl ${getStatusColor()}`}>
              {verificationStatus === "loading" && "Please wait while we verify your email address..."}
              {message}
            </p>

            {verificationStatus === "success" && (
              <div className="space-y-6">
                <div className="rounded-lg border border-green-200 bg-white p-6">
                  <h3 className="mb-4 text-lg font-semibold text-green-800">What's Next?</h3>
                  <ul className="space-y-2 text-left text-base text-green-700">
                    <li>• You now have full access to all conference features</li>
                    <li>• You can register for sessions and events</li>
                    <li>• Download conference materials and certificates</li>
                    <li>• Access exclusive member content</li>
                  </ul>
                </div>

                <div className="flex flex-col gap-4 sm:flex-row">
                  <Button
                    onClick={handleContinue}
                    className="flex-1 bg-green-600 py-3 text-base text-white hover:bg-green-700"
                  >
                    <i className="fas fa-tachometer-alt mr-2"></i>
                    Go to Dashboard
                  </Button>
                  <Link href="/profile" className="flex-1">
                    <Button
                      variant="outline"
                      className="w-full border-green-300 py-3 text-base text-green-700 hover:bg-green-50"
                    >
                      <i className="fas fa-user mr-2"></i>
                      View Profile
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {verificationStatus === "error" && (
              <div className="space-y-6">
                <div className="rounded-lg border border-red-200 bg-white p-6">
                  <h3 className="mb-4 text-lg font-semibold text-red-800">Need Help?</h3>
                  <p className="text-base text-red-700">
                    If you continue to experience issues, please contact our support team or try requesting a new
                    verification email.
                  </p>
                </div>

                <div className="flex flex-col gap-4 sm:flex-row">
                  <Link href="/contact" className="flex-1">
                    <Button
                      variant="outline"
                      className="w-full border-red-300 py-3 text-base text-red-700 hover:bg-red-50"
                    >
                      <i className="fas fa-life-ring mr-2"></i>
                      Contact Support
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {verificationStatus === "invalid" && (
              <div className="space-y-6">
                <div className="rounded-lg border border-amber-200 bg-white p-6">
                  <h3 className="mb-4 text-lg font-semibold text-amber-800">Invalid or Expired Link</h3>
                  <p className="text-base text-amber-700">
                    This verification link may be invalid, expired, or already used. Please request a new verification
                    email.
                  </p>
                </div>

                <div className="flex flex-col gap-4 sm:flex-row">
                  <Link href="/login" className="flex-1">
                    <Button
                      variant="outline"
                      className="w-full border-amber-300 py-3 text-base text-amber-700 hover:bg-amber-50"
                    >
                      <i className="fas fa-sign-in-alt mr-2"></i>
                      Back to Login
                    </Button>
                  </Link>
                </div>
              </div>
            )}

            {verificationStatus === "loading" && (
              <div className="text-base text-gray-600">
                <p>This may take a few moments...</p>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
